@model List<ShoeShopAnNhien.Models.Order>
@{
    ViewData["Title"] = "Quản Lý Đơn Hàng - Admin Panel";
    Layout = "_AdminLayout";
}

<!-- Page Header -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="h3 mb-0">Quản Lý Đơn Hàng</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Admin")">Dashboard</a></li>
                    <li class="breadcrumb-item active">Đơn Hàng</li>
                </ol>
            </nav>
        </div>
        <div>
            <button class="btn btn-outline-secondary" onclick="exportToCSV('ordersTable', 'orders.csv')">
                <i class="fas fa-download me-2"></i><PERSON><PERSON><PERSON>
            </button>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <input type="text" class="form-control" id="searchOrders" placeholder="Tìm kiếm đơn hàng..." data-table-search="ordersTable">
            </div>
            <div class="col-md-2">
                <select class="form-select" id="filterStatus">
                    <option value="">Tất cả trạng thái</option>
                    <option value="pending">Chờ xử lý</option>
                    <option value="confirmed">Đã xác nhận</option>
                    <option value="processing">Đang xử lý</option>
                    <option value="shipping">Đang giao hàng</option>
                    <option value="delivered">Đã giao hàng</option>
                    <option value="cancelled">Đã hủy</option>
                </select>
            </div>
            <div class="col-md-2">
                <input type="date" class="form-control" id="filterDateFrom" placeholder="Từ ngày">
            </div>
            <div class="col-md-2">
                <input type="date" class="form-control" id="filterDateTo" placeholder="Đến ngày">
            </div>
            <div class="col-md-3">
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-secondary" onclick="exportToCSV('ordersTable', 'orders.csv')">
                        <i class="fas fa-download me-1"></i>Xuất CSV
                    </button>
                    <button class="btn btn-outline-secondary" onclick="printTable('ordersTable')">
                        <i class="fas fa-print me-1"></i>In
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Orders Table -->
<div class="card admin-table">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Danh Sách Đơn Hàng (@ViewBag.TotalItems đơn hàng)</h5>
        <div class="d-flex align-items-center">
            <span class="text-muted me-3">Trang @ViewBag.CurrentPage / @ViewBag.TotalPages</span>
        </div>
    </div>
    <div class="card-body p-0">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="ordersTable">
                    <thead>
                        <tr>
                            <th>Mã Đơn</th>
                            <th>Khách Hàng</th>
                            <th>Email</th>
                            <th>Số Điện Thoại</th>
                            <th>Tổng Tiền</th>
                            <th>Trạng Thái</th>
                            <th>Thanh Toán</th>
                            <th>Ngày Đặt</th>
                            <th>Thao Tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var order in Model)
                        {
                            <tr>
                                <td>
                                    <strong>#@order.OrderNumber</strong>
                                    <br><small class="text-muted">@order.OrderDetails.Sum(od => od.Quantity) sản phẩm</small>
                                </td>
                                <td>
                                    <div>
                                        <strong>@order.CustomerName</strong>
                                        <br><small class="text-muted">@order.User?.FullName</small>
                                    </div>
                                </td>
                                <td>@order.CustomerEmail</td>
                                <td>@order.CustomerPhone</td>
                                <td>
                                    <strong class="text-success">@order.TotalAmount.ToString("N0") VNĐ</strong>
                                    @if (order.Discount > 0)
                                    {
                                        <br><small class="text-muted">Giảm: @order.Discount.ToString("N0") VNĐ</small>
                                    }
                                </td>
                                <td>
                                    @{
                                        var statusClass = order.Status switch
                                        {
                                            OrderStatus.Pending => "bg-warning",
                                            OrderStatus.Confirmed => "bg-info",
                                            OrderStatus.Processing => "bg-primary",
                                            OrderStatus.Shipping => "bg-secondary",
                                            OrderStatus.Delivered => "bg-success",
                                            OrderStatus.Cancelled => "bg-danger",
                                            OrderStatus.Returned => "bg-dark",
                                            _ => "bg-secondary"
                                        };
                                        
                                        var statusText = order.Status switch
                                        {
                                            OrderStatus.Pending => "Chờ xử lý",
                                            OrderStatus.Confirmed => "Đã xác nhận",
                                            OrderStatus.Processing => "Đang xử lý",
                                            OrderStatus.Shipping => "Đang giao",
                                            OrderStatus.Delivered => "Đã giao",
                                            OrderStatus.Cancelled => "Đã hủy",
                                            OrderStatus.Returned => "Đã trả",
                                            _ => "Không xác định"
                                        };
                                    }
                                    <span class="badge @statusClass">@statusText</span>
                                </td>
                                <td>
                                    @{
                                        var paymentClass = order.PaymentStatus switch
                                        {
                                            PaymentStatus.Pending => "bg-warning",
                                            PaymentStatus.Paid => "bg-success",
                                            PaymentStatus.Failed => "bg-danger",
                                            PaymentStatus.Refunded => "bg-info",
                                            _ => "bg-secondary"
                                        };
                                        
                                        var paymentText = order.PaymentStatus switch
                                        {
                                            PaymentStatus.Pending => "Chờ thanh toán",
                                            PaymentStatus.Paid => "Đã thanh toán",
                                            PaymentStatus.Failed => "Thất bại",
                                            PaymentStatus.Refunded => "Đã hoàn tiền",
                                            _ => "Không xác định"
                                        };
                                    }
                                    <span class="badge @paymentClass">@paymentText</span>
                                </td>
                                <td>
                                    @order.CreatedAt.ToString("dd/MM/yyyy")
                                    <br><small class="text-muted">@order.CreatedAt.ToString("HH:mm")</small>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-outline-primary btn-sm" data-bs-toggle="tooltip" title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        
                                        @if (order.Status == OrderStatus.Pending)
                                        {
                                            <button class="btn btn-success btn-sm" 
                                                    onclick="updateOrderStatus(@order.Id, @((int)OrderStatus.Confirmed))" 
                                                    data-bs-toggle="tooltip" 
                                                    title="Xác nhận đơn hàng">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        }
                                        
                                        @if (order.Status == OrderStatus.Confirmed)
                                        {
                                            <button class="btn btn-primary btn-sm" 
                                                    onclick="updateOrderStatus(@order.Id, @((int)OrderStatus.Processing))" 
                                                    data-bs-toggle="tooltip" 
                                                    title="Bắt đầu xử lý">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                        }
                                        
                                        @if (order.Status == OrderStatus.Processing)
                                        {
                                            <button class="btn btn-secondary btn-sm" 
                                                    onclick="updateOrderStatus(@order.Id, @((int)OrderStatus.Shipping))" 
                                                    data-bs-toggle="tooltip" 
                                                    title="Giao hàng">
                                                <i class="fas fa-truck"></i>
                                            </button>
                                        }
                                        
                                        @if (order.Status == OrderStatus.Shipping)
                                        {
                                            <button class="btn btn-success btn-sm" 
                                                    onclick="updateOrderStatus(@order.Id, @((int)OrderStatus.Delivered))" 
                                                    data-bs-toggle="tooltip" 
                                                    title="Hoàn thành">
                                                <i class="fas fa-check-circle"></i>
                                            </button>
                                        }
                                        
                                        @if (order.Status != OrderStatus.Delivered && order.Status != OrderStatus.Cancelled)
                                        {
                                            <button class="btn btn-outline-danger btn-sm" 
                                                    onclick="updateOrderStatus(@order.Id, @((int)OrderStatus.Cancelled))" 
                                                    data-bs-toggle="tooltip" 
                                                    title="Hủy đơn hàng">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart text-muted" style="font-size: 4rem; opacity: 0.3;"></i>
                <h5 class="mt-3 text-muted">Chưa có đơn hàng nào</h5>
                <p class="text-muted">Đơn hàng sẽ xuất hiện ở đây khi khách hàng đặt mua</p>
            </div>
        }
    </div>
</div>

<!-- Pagination -->
@if (ViewBag.TotalPages > 1)
{
    <nav aria-label="Orders pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            @if (ViewBag.CurrentPage > 1)
            {
                <li class="page-item">
                    <a class="page-link" href="@Url.Action("Orders", new { page = ViewBag.CurrentPage - 1 })">
                        <i class="fas fa-chevron-left"></i> Trước
                    </a>
                </li>
            }

            @for (int i = Math.Max(1, ViewBag.CurrentPage - 2); i <= Math.Min(ViewBag.TotalPages, ViewBag.CurrentPage + 2); i++)
            {
                <li class="page-item @(i == ViewBag.CurrentPage ? "active" : "")">
                    <a class="page-link" href="@Url.Action("Orders", new { page = i })">@i</a>
                </li>
            }

            @if (ViewBag.CurrentPage < ViewBag.TotalPages)
            {
                <li class="page-item">
                    <a class="page-link" href="@Url.Action("Orders", new { page = ViewBag.CurrentPage + 1 })">
                        Sau <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            }
        </ul>
    </nav>
}
