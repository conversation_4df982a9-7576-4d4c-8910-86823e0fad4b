@model List<ShoeShopAnNhien.Models.ApplicationUser>
@{
    ViewData["Title"] = "Quản Lý Khá<PERSON> Hàng - Admin Panel";
    Layout = "_AdminLayout";
}

<!-- Page Header -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="h3 mb-0">Quản Lý K<PERSON></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Admin")">Dashboard</a></li>
                    <li class="breadcrumb-item active">Kh<PERSON>ch Hàng</li>
                </ol>
            </nav>
        </div>
        <div>
            <button class="btn btn-outline-secondary" onclick="exportToCSV('customersTable', 'customers.csv')">
                <i class="fas fa-download me-2"></i><PERSON><PERSON><PERSON>
            </button>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <input type="text" class="form-control" id="searchCustomers" placeholder="Tìm kiếm khách hàng..." data-table-search="customersTable">
            </div>
            <div class="col-md-2">
                <select class="form-select" id="filterStatus">
                    <option value="">Tất cả trạng thái</option>
                    <option value="active">Hoạt động</option>
                    <option value="inactive">Tạm khóa</option>
                </select>
            </div>
            <div class="col-md-2">
                <input type="date" class="form-control" id="filterDateFrom" placeholder="Từ ngày đăng ký">
            </div>
            <div class="col-md-2">
                <input type="date" class="form-control" id="filterDateTo" placeholder="Đến ngày">
            </div>
            <div class="col-md-2">
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-secondary" onclick="exportToCSV('customersTable', 'customers.csv')">
                        <i class="fas fa-download me-1"></i>CSV
                    </button>
                    <button class="btn btn-outline-secondary" onclick="printTable('customersTable')">
                        <i class="fas fa-print me-1"></i>In
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Customers Table -->
<div class="card admin-table">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Danh Sách Khách Hàng (@ViewBag.TotalItems khách hàng)</h5>
        <div class="d-flex align-items-center">
            <span class="text-muted me-3">Trang @ViewBag.CurrentPage / @ViewBag.TotalPages</span>
        </div>
    </div>
    <div class="card-body p-0">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="customersTable">
                    <thead>
                        <tr>
                            <th>Avatar</th>
                            <th>Thông Tin Khách Hàng</th>
                            <th>Email</th>
                            <th>Số Điện Thoại</th>
                            <th>Địa Chỉ</th>
                            <th>Ngày Đăng Ký</th>
                            <th>Lần Cuối Đăng Nhập</th>
                            <th>Trạng Thái</th>
                            <th>Thao Tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var customer in Model)
                        {
                            <tr>
                                <td>
                                    <div class="avatar-placeholder bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                                         style="width: 40px; height: 40px;">
                                        <i class="fas fa-user"></i>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>@customer.FullName</strong>
                                        <br><small class="text-muted">@customer.UserName</small>
                                        @if (!string.IsNullOrEmpty(customer.Gender))
                                        {
                                            <br><small class="text-muted">@customer.Gender</small>
                                        }
                                    </div>
                                </td>
                                <td>
                                    @customer.Email
                                    @if (customer.EmailConfirmed)
                                    {
                                        <i class="fas fa-check-circle text-success ms-1" data-bs-toggle="tooltip" title="Email đã xác thực"></i>
                                    }
                                    else
                                    {
                                        <i class="fas fa-exclamation-circle text-warning ms-1" data-bs-toggle="tooltip" title="Email chưa xác thực"></i>
                                    }
                                </td>
                                <td>
                                    @(customer.PhoneNumber ?? "Chưa cập nhật")
                                    @if (customer.PhoneNumberConfirmed && !string.IsNullOrEmpty(customer.PhoneNumber))
                                    {
                                        <i class="fas fa-check-circle text-success ms-1" data-bs-toggle="tooltip" title="SĐT đã xác thực"></i>
                                    }
                                </td>
                                <td>
                                    @(customer.Address ?? "Chưa cập nhật")
                                </td>
                                <td>
                                    @customer.CreatedAt.ToString("dd/MM/yyyy")
                                    <br><small class="text-muted">@customer.CreatedAt.ToString("HH:mm")</small>
                                </td>
                                <td>
                                    @if (customer.LastLoginAt.HasValue)
                                    {
                                        @customer.LastLoginAt.Value.ToString("dd/MM/yyyy")
                                        <br><small class="text-muted">@customer.LastLoginAt.Value.ToString("HH:mm")</small>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Chưa đăng nhập</span>
                                    }
                                </td>
                                <td>
                                    <span class="badge @(customer.IsActive ? "bg-success" : "bg-danger")">
                                        @(customer.IsActive ? "Hoạt động" : "Tạm khóa")
                                    </span>
                                    @if (customer.LockoutEnd.HasValue && customer.LockoutEnd > DateTime.Now)
                                    {
                                        <br><span class="badge bg-warning">Bị khóa</span>
                                    }
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-outline-primary btn-sm" data-bs-toggle="tooltip" title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" data-bs-toggle="tooltip" title="Xem đơn hàng">
                                            <i class="fas fa-shopping-bag"></i>
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm" data-bs-toggle="tooltip" title="Gửi email">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                        @if (customer.IsActive)
                                        {
                                            <button class="btn btn-outline-danger btn-sm" data-bs-toggle="tooltip" title="Tạm khóa">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                        }
                                        else
                                        {
                                            <button class="btn btn-outline-success btn-sm" data-bs-toggle="tooltip" title="Kích hoạt">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="fas fa-users text-muted" style="font-size: 4rem; opacity: 0.3;"></i>
                <h5 class="mt-3 text-muted">Chưa có khách hàng nào</h5>
                <p class="text-muted">Khách hàng sẽ xuất hiện ở đây khi họ đăng ký tài khoản</p>
            </div>
        }
    </div>
</div>

<!-- Pagination -->
@if (ViewBag.TotalPages > 1)
{
    <nav aria-label="Customers pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            @if (ViewBag.CurrentPage > 1)
            {
                <li class="page-item">
                    <a class="page-link" href="@Url.Action("Customers", new { page = ViewBag.CurrentPage - 1 })">
                        <i class="fas fa-chevron-left"></i> Trước
                    </a>
                </li>
            }

            @for (int i = Math.Max(1, ViewBag.CurrentPage - 2); i <= Math.Min(ViewBag.TotalPages, ViewBag.CurrentPage + 2); i++)
            {
                <li class="page-item @(i == ViewBag.CurrentPage ? "active" : "")">
                    <a class="page-link" href="@Url.Action("Customers", new { page = i })">@i</a>
                </li>
            }

            @if (ViewBag.CurrentPage < ViewBag.TotalPages)
            {
                <li class="page-item">
                    <a class="page-link" href="@Url.Action("Customers", new { page = ViewBag.CurrentPage + 1 })">
                        Sau <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            }
        </ul>
    </nav>
}

<!-- Customer Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">@Model.Count(c => c.IsActive)</h5>
                <p class="card-text">Khách Hàng Hoạt Động</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">@Model.Count(c => c.CreatedAt >= DateTime.Now.AddDays(-30))</h5>
                <p class="card-text">Đăng Ký Tháng Này</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">@Model.Count(c => c.EmailConfirmed)</h5>
                <p class="card-text">Email Đã Xác Thực</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">@Model.Count(c => c.LastLoginAt >= DateTime.Now.AddDays(-7))</h5>
                <p class="card-text">Hoạt Động Tuần Này</p>
            </div>
        </div>
    </div>
</div>
