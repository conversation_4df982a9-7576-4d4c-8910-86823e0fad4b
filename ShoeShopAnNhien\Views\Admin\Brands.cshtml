@model List<ShoeShopAnNhien.Models.Brand>
@{
    ViewData["Title"] = "Quản Lý Thương Hiệu - Admin Panel";
    Layout = "_AdminLayout";
}

<!-- Page Header -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="h3 mb-0">Quản Lý Thương Hiệu</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Admin")">Dashboard</a></li>
                    <li class="breadcrumb-item active">Thương Hiệu</li>
                </ol>
            </nav>
        </div>
        <div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBrandModal">
                <i class="fas fa-plus me-2"></i>Th<PERSON><PERSON>
            </button>
        </div>
    </div>
</div>

<!-- Brands Grid -->
<div class="row">
    @if (Model.Any())
    {
        @foreach (var brand in Model)
        {
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">@brand.Name</h6>
                        <span class="badge @(brand.IsActive ? "bg-success" : "bg-secondary")">
                            @(brand.IsActive ? "Hoạt động" : "Tạm dừng")
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <img src="@(brand.LogoUrl ?? "/images/brands/default.png")" 
                                 alt="@brand.Name" 
                                 class="img-fluid" 
                                 style="width: 120px; height: 80px; object-fit: contain;">
                        </div>
                        <p class="card-text">@(brand.Description ?? "Chưa có mô tả")</p>
                        <div class="row text-center">
                            <div class="col-6">
                                <h6 class="text-primary">@brand.Products.Count</h6>
                                <small class="text-muted">Sản phẩm</small>
                            </div>
                            <div class="col-6">
                                <h6 class="text-success">@brand.CreatedAt.ToString("dd/MM/yyyy")</h6>
                                <small class="text-muted">Ngày tạo</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <button class="btn btn-outline-primary btn-sm" data-bs-toggle="tooltip" title="Chỉnh sửa">
                                <i class="fas fa-edit"></i> Sửa
                            </button>
                            <button class="btn btn-outline-@(brand.IsActive ? "secondary" : "success") btn-sm" 
                                    data-bs-toggle="tooltip" 
                                    title="@(brand.IsActive ? "Tạm dừng" : "Kích hoạt")">
                                <i class="fas fa-@(brand.IsActive ? "pause" : "play")"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm" data-bs-toggle="tooltip" title="Xóa">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-trademark text-muted" style="font-size: 4rem; opacity: 0.3;"></i>
                <h5 class="mt-3 text-muted">Chưa có thương hiệu nào</h5>
                <p class="text-muted">Hãy thêm thương hiệu đầu tiên cho cửa hàng</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBrandModal">
                    <i class="fas fa-plus me-2"></i>Thêm Thương Hiệu
                </button>
            </div>
        </div>
    }
</div>

<!-- Brands Table View -->
<div class="card admin-table mt-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Danh Sách Chi Tiết</h5>
        <div>
            <button class="btn btn-outline-secondary btn-sm" onclick="exportToCSV('brandsTable', 'brands.csv')">
                <i class="fas fa-download me-1"></i>Xuất CSV
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="brandsTable">
                    <thead>
                        <tr>
                            <th>Logo</th>
                            <th>Tên Thương Hiệu</th>
                            <th>Mô Tả</th>
                            <th>Số Sản Phẩm</th>
                            <th>Trạng Thái</th>
                            <th>Ngày Tạo</th>
                            <th>Thao Tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var brand in Model)
                        {
                            <tr>
                                <td>
                                    <img src="@(brand.LogoUrl ?? "/images/brands/default.png")" 
                                         alt="@brand.Name" 
                                         style="width: 60px; height: 40px; object-fit: contain;">
                                </td>
                                <td>
                                    <strong>@brand.Name</strong>
                                </td>
                                <td>
                                    @(brand.Description ?? "Chưa có mô tả")
                                </td>
                                <td>
                                    <span class="badge bg-primary">@brand.Products.Count sản phẩm</span>
                                </td>
                                <td>
                                    <span class="badge @(brand.IsActive ? "bg-success" : "bg-secondary")">
                                        @(brand.IsActive ? "Hoạt động" : "Tạm dừng")
                                    </span>
                                </td>
                                <td>
                                    @brand.CreatedAt.ToString("dd/MM/yyyy")
                                    <br><small class="text-muted">@brand.CreatedAt.ToString("HH:mm")</small>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-outline-primary btn-sm" data-bs-toggle="tooltip" title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" data-bs-toggle="tooltip" title="Xem sản phẩm">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" data-bs-toggle="tooltip" title="Xóa">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
    </div>
</div>

<!-- Add Brand Modal -->
<div class="modal fade" id="addBrandModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm Thương Hiệu Mới</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addBrandForm">
                    <div class="mb-3">
                        <label class="form-label">Tên Thương Hiệu *</label>
                        <input type="text" class="form-control" name="Name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mô Tả</label>
                        <textarea class="form-control" name="Description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Logo</label>
                        <input type="file" class="form-control" name="LogoFile" accept="image/*">
                        <small class="form-text text-muted">Chọn logo thương hiệu (khuyến nghị: PNG với nền trong suốt)</small>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="IsActive" id="isActiveBrand" checked>
                            <label class="form-check-label" for="isActiveBrand">
                                Kích hoạt thương hiệu
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="saveBrand()">
                    <i class="fas fa-save me-2"></i>Lưu Thương Hiệu
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Brand Statistics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">@Model.Count(b => b.IsActive)</h5>
                <p class="card-text">Thương Hiệu Hoạt Động</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">@Model.Sum(b => b.Products.Count)</h5>
                <p class="card-text">Tổng Sản Phẩm</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">@(Model.Any() ? Model.OrderByDescending(b => b.Products.Count).First().Name : "N/A")</h5>
                <p class="card-text">Thương Hiệu Nhiều SP Nhất</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">@Model.Count(b => b.CreatedAt >= DateTime.Now.AddDays(-30))</h5>
                <p class="card-text">Thêm Mới Tháng Này</p>
            </div>
        </div>
    </div>
</div>

<script>
function saveBrand() {
    // This would normally submit to server
    showToast('Chức năng thêm thương hiệu sẽ được phát triển trong phiên bản tiếp theo!', 'info');
}
</script>
