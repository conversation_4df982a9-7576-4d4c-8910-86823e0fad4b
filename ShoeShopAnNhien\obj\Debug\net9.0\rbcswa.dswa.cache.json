{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["/rIHRNIHtxjbuloLMOb0ZB+51BOn/P0soq4xwiZVr2U=", "ohgh5PyM1uPqYCuv9a01aKBY4CVqKbrcUjex0Aj7Mb0=", "2bqaXy4DvblUPb5wUZ6EqiBljKRDpVBVZfVGWRDRcd8=", "G9B0Ojg8zJZH1MstSQFVle8wciucVhZQuXF+rVZBaRo=", "b4i7bgFLejysUi+J7O8/zaA2bwUDNIwsz2555CdMskY=", "rlMtyuNuL6Tg3FtE7jEmeM2gB6zYdXQRlZ8xg7QtR+0=", "18JTlagAfGgCpzYfONXVQm6Zsr1L/nzKF+LRZnQtWCU=", "b0EMkYOV7AaTZRFKGv3GYA63J/T83eNwN1oDmXkP+eU=", "rXX6axpiIXIE5WFxTupz4jK3di6kniBqbfGSdE3GXJA=", "eqbC6X31DtKUaXWxV5RI1mmpCyCysMmjMYMXZ14ga6I=", "w37LnTWHF2n1YqjMiCLw4SNWqFeLEDFHwZGa7VqJ6Bk=", "7wvzl+SM5xzF/ZHOoehHfiIVUs4Zr7jW9DofJbH5Zxo=", "W8FIc2bUtpAPGfOEyDNc0RJujQdgr2sM/ADbKX2OXo8=", "A4WNnaiftvGGmWHc+F0+13qL8NXZAm6oQZ4ULj9BMJU=", "3A460W7cKJVhQW3IwPwlZNqpuIE/ZSxWAFGZDHdivPI=", "HY3sVhW2spfsmCthHB0C+RE3gyCB5cuIxtwVjnD8L+E=", "OeBf2yJgpW5c1OnXgkhp++0dnP+atg9ul1NlyyTZEoo=", "/0/FSCqcYI6BL3YpEyXAic3SH0t8IxAzUe/BRI3QoKY=", "TyfTwbV8g0F2EosWF+f6m0vmOiMgfl58I7syF9Az9hM=", "ZN/b/i0akoXUyQANAGHB9UxTV0UENznH1fu1VICXIV0=", "llaM/gzlbJ0KZ/u/AHDdd/1B11yOXoWHTKUq22y2mas=", "imIQgUgPFEwTXlh9C0Lzd4AMHqCOMDE+VtDEm3GEsxQ=", "LRDMD7V7LplRh5sXBPAlKPte94u8jcgJFI+mfXfAOlA=", "pF0FeeLy+m/YGY7Bjaw96QAICcdaivoCtT3mEhH9Cf0=", "+AjvAoasLi3CFL7W3Sdenrhungt24WtDkG8OXreRXO8=", "ZY1HOHexppitoKBtH30YkRT0u6EEynt/ZG7DTM/1hGY=", "drNipXPmbTy9Nd4AQs4jPh7gGIPokjQaJsMz67izx08=", "b74/mc1g5aqwRd9/3AtQa5wuiKz4werX4e/EBRJa2UE=", "5KFRDP8Y5dqZ7tGhpNoLduL++kn6S+kU4FLgPHMLLE0=", "psMfXXJ5AngKbviT7Dgq8gVqDjIyMYdVUklAVGQL0EI=", "a3jZawNd/9lHZKYEHi3xRWG5r0AZ9/ZrKY5/VzQoyL4=", "AEHFOWCunBVtGzMopWihTacZIJLbadNPgyL4PEXCIrc=", "ROK/wd0tvA2gtTGEF8dJx5tDt1brf/dMMdb4QFjAB84=", "obUh1d22RpDObCm6YXp/D6+j6kufzGC+aslOXRXBZVQ=", "H7gGYQWAx0nTcMc/l+nausBzD2Q8kLDbH9NQKI+2l80=", "xzO34L5cJT2QnKDWx/4DIjUbizLffwHiDYjvFqE7zu8=", "Hsm6Y2APnKNGq5/bqxAiPJJc92pM+TfEv79KABivCxk=", "bNa12vX/w0fEVpNsxM4aO4VeJS7U4jv3IS+dqppIM5o=", "0cKGuL5Wii84oW9nJuAk1ufVuLsMRzdQJl19WhEjW0g=", "UqE68cz7dTdyAE+2gzBowpPOiE/77Ee6n+cTTikVGfU=", "EWZv23h9hKUrTQXnFU3rUq80FUDAjvf0Cft1ao5Njj8=", "vIvJSPdRySL+PIufITB8pgq6ffuclf/ZQp7lr/FJnOk=", "XzE/uaywNHch+qo+eyOz+3pe/ISZQ48kQPqDZXbNNLo=", "ihfV1tICVjY0a94vWCa4aAlmEFvJzLP9uTavLTndm3w=", "Z+ScYp9W4u3NDl5S0huqbUymLJaySH8ncZ1DhQE4L/M=", "NdgKqiJtkTLvtNk1qN2wjlaEVGFnpGbYRMKPOQ7NP38=", "hU1jTUaHiypB9tKQPMzRl3JFuQ0EiGgVBkPEyhsr3is=", "M5UnaV96doN+l7uwv0E8Yiz1LYtPa6ZW2z1yGGCZ5DQ=", "pKqM1oKJfK+7+BhxzoORn0U1q8l2Qas5tJbwWuvFCf8=", "hSnMYGcLXm9P0ZEAWxVGuBtG8IhmW27jgi41NBSTnHo=", "gT36z4HbN2CJRlWbjF6HwxtHZS/kJW9u6OA5nqhDnbU=", "dOEt/Oz7LZNDReP0wyf4hca1VZf5CDxL0tclqObTqEA=", "x9LKzbUJD3MdaLm92Tq4f/r4wdzdFf5jzsbE2/fRA78=", "gIU+7dO3+t1mq7o4crMGZ/UTdYY/5dTmN3t6FzhHqB8=", "EGoKIM7dAmMDUyS8Rcadppwp1otZ131+zPjyzTS09Lc=", "wtLJuD4f+7VmhO60k3aLSshYKpQ5vKT8PFpM96bPunA=", "sUjQA/IRv5Z2uEPu/+yfLyob3nO3BXFS6TCzvdvL3Z4=", "6My8bZpKoPvudtgJ/vLin0MjVHSRtdaRUO44IbtpRiQ=", "ABvge/VxJNziWTfKsyJWabFapDn6gO3tCkiQFkTJOrw=", "P8jH2zD81Y0T6w+hJgqRSuC474nP7RonTxmoJ/pUuXI=", "CNzJLpF+Pqb18V0Irm6sY0lSjpK9L+y5AqjuTn7cZtY=", "L2cUlVEid2TrG3XKw47WaaTQuERiTljAYcL5h1Ht8qo=", "8JJQhUHi1HlyaiiFM0oASYKbwN03KBxBWV+UiYwL2X4=", "0fDnl6CMTQ3VrIoQWfvSDiTfCfR3r2GC39wMwrRNNcI=", "41Sjivqydu5dpTYLUCGr4U80H6eiLHdcwe8c0xDQxrY=", "Fnv0UEnYcITwoSvJqGs7W7yFD0kuCHja7fV1sB43srU=", "I2QUmlFTwLPPj4Gw0CiC4EqWjOdNQfuYl0QU1E+F3F0=", "sA5NfNgfKl0nknXXWx1l8uJTbNFRkpZT1EtlMUU9C0U=", "ZQit54vtl6txxZfO7nY3WTCLUgeLOkZJ8wLvq7+Eh6I=", "7rA1/iEJfcqpXxtWx+LubNgyzPiYruogeA3BMVPccn4=", "JJGlQ5bbK9DLuYr3wR9UQxWzujE5vl57Zx1deiqvp9I=", "jvGhUFhkz5gsHlokUJf3QuEDhHZ46VIRLFgNXEgo1ec=", "ZVPaS/PKxUrm+e8vcnNE65apFQ0/tNLT9ERxMJGfUWI=", "mZnE7vrsjqMJzjfndPUhgdA0XVAtfmRhAFKRYaF2bRs=", "ZG1i5KKSOiwUcqmRGa4FP7Ke8sdfewXOX8Zp6+Hf8jE=", "r5c6oy92TuIF5fOp6DQVUypu9Wni7+SdjFwVMTz5K/s=", "iYGpUGeuCQ9zEJnsstcDVsDVyeFVPRYOfQC23nbmJNE=", "CkAbxyGB4LD0KNDtU5rg9nPh5WbSEU29uwg8MrfCR1o=", "0/j8MSKoT9BYWJtJE5oirMbQXN7NGCBdVodjfccHQJU=", "7BvisFV0sAs5As/3KgpVD1CQo7+mb6coy62qnY1OMDQ=", "pRzta8bFsuw26GhN/nAPRopY5G+qzwgtKJo9KmLiGf0=", "uVxzfgr6mCfFjm/6E0wCnlht8ZiKBfVQop3Vi75QZUU=", "DUzPw45+B+b84hOAN16xtdX49MPL1zu7rZLlMq6rJkM=", "CuOTUAFGhd43URiMU0KuZQgAD4OiSDs22f19QAORXZI=", "aEo4tFpSc5QLco6NmrU2OJUqOVLEHb1416yRdrfBENQ=", "OuNanHqbzQ5ohV+ClhiDjFsUvyRL4mvHopTCK93zDvE=", "hlMD9MMp2G+98KuNxJcANGrNFCpLMRxpD2aOOQ4gLuE=", "57+jU0dRO9jFpbcmrHF8UGLIR7SNjqZJgCBCTWI17Y0=", "9sw2oXPa+JWOg9GRow4FPG+XoeQKwJMFg77ReoJ9eeI=", "btfkzqNlCtR4h2ASOZD+5OWjfNyhF+je3b+l19G4VPk=", "sbAOoYTD4fvf7SzvyRyYXMRBg7YC/7BZLyW/jg7VONk=", "X8ckCuwf2WGkMdSaDa7blecSoT0c9ZuKOi7DAd7aosQ=", "sLixJgmzD1P1oBwkxzZmh3huZaXJUqb6zE1nkUV/Rms=", "hduGZzBSrLWLvPRlJS94PvYKmb08tq8bj77VVxzzYOQ=", "cwP6wQXVtKtnY4ZR97/z9SlIxvn4wQeLnK61btVmOdE=", "HFZnV+XiLFpUNoMMAjJX/FTzlbAsYHUEKnD3d4O7siw=", "JzKMrRznersEjJV/umWv3lUIAxdWAql4LyhAnllOwXs=", "+fXZ9y2CdvVWGx3LHyDCIqK6JkVZX2lUVWa4Esz4vNU=", "CYKuLubPKWT7Hvy5fSYH54o2ZaA5jBrHrnVWDl+c2Xk=", "uQPWZRqGRXZWGdQhEhUStABvKGEC7UtpHxIjZpkED3U=", "EkwF6e3JM2etW9njQ3SpTYodl4CBbICjx3z9cqWd8Oo=", "JRsGsWXWFF36anjM/SzF5NKLQ1aMg8Z9g/LH4j2uMkA=", "a4YhvPNU5RZmok2U0CR0H1dyfDxjaU5onBzpNUFi3xU=", "ofqjlFb4jBoHTMN/c25t7rpuhi6aihvKmxHMgPZe7Ks=", "c8ttY8UYcI5Va1e1TDPnEcF8exRDNP/3FdJDJVkzmzE=", "iWbdY6PPgtxOppxQK22Dy+hcDX0oI7mA2Y0G1EdQgCg=", "Kvc+Rqw6ItjHpPr4pauFRD6Ozrmhze/k+/6NQ6e0x/Q=", "d6NgOCb5Ec3ksYVmzKaXHjOm0zklMGGLydq8Q5d6LNI=", "Ys96MAzd6yBcJT+AnB7IaiCY6yjlcjN8iZFpjPMWlgg=", "Pt8+EZz/7i50+RxLcHk+tBkUlJOPIYtnOT0ru7GMK7A=", "czxyNFgUlG3clEZVOeSBwDcts+s0sBq2JBFqxepRYJQ=", "V3hC2sXYPs/htHXMap1QNedRtX4KveE2dYSuU1FyC7M=", "KVDHHlTv9w1vhdW03l9kB6QnYMYugZXt60J1w30ELZM=", "jJ8pATp9GcCrpPLpPGm1hGbDV480NdQxJO/cq9SQjlY=", "nfsi6GywDn3g7ziUNLtdfu41F7q4+UvinDjma5Demgw=", "DWDzfv/4F4SysBm/dOD63mQwxs4gO3GJRAbEiZHDS88=", "lswOGmjE8VED2PrO2kc7e3XleYbiNjBmu+Mk9V2lP50=", "f7NujZZ2zkEV1DLmL24sq1iQnfq62drB4AYYUXUfRbo=", "UaL7XmGJo6NsWLAYNzAlDrKi2ZfuJApRjabESu+t26E=", "tDbywowiEUyk9D51MyAor6Ka6EOtWVD2CG3tQ4GXBjs=", "LFrP0rsjytAUvMyoyJudx25xahTRX/v1yAKzrGu9fcw=", "Kzl5aAJG/AJzkcwflST9NK/a0aP5A6iOur6h+0jjE20=", "Q/MBYbhGIVstJlJY14hUW9b191XEOi4J60hWFSiSKyw=", "THNQ6pasxvdkqKXNBCYLicsv/7g41xyQ0LeHvORIx3A=", "ya1RDkcSYejDjXd3xBnX6Bp+ACY+6rb1Z9QUFvyEnpI=", "nCmPS9dCPfLwUMagbpfjxp8frBz+rynhiwxtglb0bOs=", "SQwyy18yJ4R90pVTVAos1ng5I134t4zCH5FI5voxdhA="], "CachedAssets": {"/rIHRNIHtxjbuloLMOb0ZB+51BOn/P0soq4xwiZVr2U=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-07-15T17:21:45.8100226+00:00"}, "ohgh5PyM1uPqYCuv9a01aKBY4CVqKbrcUjex0Aj7Mb0=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-07-15T17:21:45.8120211+00:00"}, "2bqaXy4DvblUPb5wUZ6EqiBljKRDpVBVZfVGWRDRcd8=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-15T17:21:45.8446977+00:00"}, "G9B0Ojg8zJZH1MstSQFVle8wciucVhZQuXF+rVZBaRo=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-15T17:21:45.8538664+00:00"}, "b4i7bgFLejysUi+J7O8/zaA2bwUDNIwsz2555CdMskY=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-15T17:21:45.8310275+00:00"}, "rlMtyuNuL6Tg3FtE7jEmeM2gB6zYdXQRlZ8xg7QtR+0=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-15T17:21:45.8353229+00:00"}, "18JTlagAfGgCpzYfONXVQm6Zsr1L/nzKF+LRZnQtWCU=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-15T17:21:45.8383306+00:00"}, "b0EMkYOV7AaTZRFKGv3GYA63J/T83eNwN1oDmXkP+eU=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-15T17:21:45.840486+00:00"}, "rXX6axpiIXIE5WFxTupz4jK3di6kniBqbfGSdE3GXJA=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-15T17:21:45.8446977+00:00"}, "eqbC6X31DtKUaXWxV5RI1mmpCyCysMmjMYMXZ14ga6I=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-15T17:21:45.8462111+00:00"}, "w37LnTWHF2n1YqjMiCLw4SNWqFeLEDFHwZGa7VqJ6Bk=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-15T17:21:45.8150219+00:00"}, "7wvzl+SM5xzF/ZHOoehHfiIVUs4Zr7jW9DofJbH5Zxo=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-15T17:21:45.8160206+00:00"}, "W8FIc2bUtpAPGfOEyDNc0RJujQdgr2sM/ADbKX2OXo8=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-15T17:21:45.8230236+00:00"}, "A4WNnaiftvGGmWHc+F0+13qL8NXZAm6oQZ4ULj9BMJU=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-15T17:21:45.8280276+00:00"}, "3A460W7cKJVhQW3IwPwlZNqpuIE/ZSxWAFGZDHdivPI=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-15T17:21:45.8363316+00:00"}, "HY3sVhW2spfsmCthHB0C+RE3gyCB5cuIxtwVjnD8L+E=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-15T17:21:45.8393333+00:00"}, "OeBf2yJgpW5c1OnXgkhp++0dnP+atg9ul1NlyyTZEoo=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-15T17:21:45.8501389+00:00"}, "/0/FSCqcYI6BL3YpEyXAic3SH0t8IxAzUe/BRI3QoKY=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-15T17:21:45.8515511+00:00"}, "TyfTwbV8g0F2EosWF+f6m0vmOiMgfl58I7syF9Az9hM=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-15T17:21:45.8538664+00:00"}, "ZN/b/i0akoXUyQANAGHB9UxTV0UENznH1fu1VICXIV0=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-15T17:21:45.8538664+00:00"}, "llaM/gzlbJ0KZ/u/AHDdd/1B11yOXoWHTKUq22y2mas=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-15T17:21:45.8160206+00:00"}, "imIQgUgPFEwTXlh9C0Lzd4AMHqCOMDE+VtDEm3GEsxQ=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-15T17:21:45.8170215+00:00"}, "LRDMD7V7LplRh5sXBPAlKPte94u8jcgJFI+mfXfAOlA=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-15T17:21:45.821024+00:00"}, "pF0FeeLy+m/YGY7Bjaw96QAICcdaivoCtT3mEhH9Cf0=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-15T17:21:45.8245147+00:00"}, "+AjvAoasLi3CFL7W3Sdenrhungt24WtDkG8OXreRXO8=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-15T17:21:45.8383306+00:00"}, "ZY1HOHexppitoKBtH30YkRT0u6EEynt/ZG7DTM/1hGY=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-15T17:21:45.840486+00:00"}, "drNipXPmbTy9Nd4AQs4jPh7gGIPokjQaJsMz67izx08=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-15T17:21:45.843697+00:00"}, "b74/mc1g5aqwRd9/3AtQa5wuiKz4werX4e/EBRJa2UE=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-15T17:21:45.8483034+00:00"}, "5KFRDP8Y5dqZ7tGhpNoLduL++kn6S+kU4FLgPHMLLE0=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-15T17:21:45.8793759+00:00"}, "psMfXXJ5AngKbviT7Dgq8gVqDjIyMYdVUklAVGQL0EI=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-15T17:21:45.8833825+00:00"}, "a3jZawNd/9lHZKYEHi3xRWG5r0AZ9/ZrKY5/VzQoyL4=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-15T17:21:45.8190217+00:00"}, "AEHFOWCunBVtGzMopWihTacZIJLbadNPgyL4PEXCIrc=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-15T17:21:45.8230236+00:00"}, "ROK/wd0tvA2gtTGEF8dJx5tDt1brf/dMMdb4QFjAB84=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-15T17:21:45.8330288+00:00"}, "obUh1d22RpDObCm6YXp/D6+j6kufzGC+aslOXRXBZVQ=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-15T17:21:45.8538664+00:00"}, "H7gGYQWAx0nTcMc/l+nausBzD2Q8kLDbH9NQKI+2l80=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-15T17:21:45.8873847+00:00"}, "xzO34L5cJT2QnKDWx/4DIjUbizLffwHiDYjvFqE7zu8=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-15T17:21:45.8886612+00:00"}, "Hsm6Y2APnKNGq5/bqxAiPJJc92pM+TfEv79KABivCxk=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-15T17:21:45.9022325+00:00"}, "bNa12vX/w0fEVpNsxM4aO4VeJS7U4jv3IS+dqppIM5o=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-15T17:21:45.8538664+00:00"}, "0cKGuL5Wii84oW9nJuAk1ufVuLsMRzdQJl19WhEjW0g=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-15T17:21:45.8967808+00:00"}, "UqE68cz7dTdyAE+2gzBowpPOiE/77Ee6n+cTTikVGfU=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-15T17:21:45.901254+00:00"}, "EWZv23h9hKUrTQXnFU3rUq80FUDAjvf0Cft1ao5Njj8=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-15T17:21:45.8160206+00:00"}, "vIvJSPdRySL+PIufITB8pgq6ffuclf/ZQp7lr/FJnOk=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-15T17:21:45.8180216+00:00"}, "XzE/uaywNHch+qo+eyOz+3pe/ISZQ48kQPqDZXbNNLo=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-15T17:21:45.8270281+00:00"}, "ihfV1tICVjY0a94vWCa4aAlmEFvJzLP9uTavLTndm3w=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-15T17:21:45.8515511+00:00"}, "Z+ScYp9W4u3NDl5S0huqbUymLJaySH8ncZ1DhQE4L/M=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-15T17:21:45.8640017+00:00"}, "NdgKqiJtkTLvtNk1qN2wjlaEVGFnpGbYRMKPOQ7NP38=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-15T17:21:45.8781492+00:00"}, "hU1jTUaHiypB9tKQPMzRl3JFuQ0EiGgVBkPEyhsr3is=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-15T17:21:45.8886612+00:00"}, "M5UnaV96doN+l7uwv0E8Yiz1LYtPa6ZW2z1yGGCZ5DQ=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-15T17:21:45.8640017+00:00"}, "pKqM1oKJfK+7+BhxzoORn0U1q8l2Qas5tJbwWuvFCf8=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-15T17:21:45.8771351+00:00"}, "hSnMYGcLXm9P0ZEAWxVGuBtG8IhmW27jgi41NBSTnHo=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-15T17:21:45.8781492+00:00"}, "gT36z4HbN2CJRlWbjF6HwxtHZS/kJW9u6OA5nqhDnbU=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-15T17:21:45.813021+00:00"}, "dOEt/Oz7LZNDReP0wyf4hca1VZf5CDxL0tclqObTqEA=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-15T17:21:45.8160206+00:00"}, "x9LKzbUJD3MdaLm92Tq4f/r4wdzdFf5jzsbE2/fRA78=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-15T17:21:45.8200223+00:00"}, "gIU+7dO3+t1mq7o4crMGZ/UTdYY/5dTmN3t6FzhHqB8=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-15T17:21:45.821024+00:00"}, "EGoKIM7dAmMDUyS8Rcadppwp1otZ131+zPjyzTS09Lc=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-15T17:21:45.8310275+00:00"}, "wtLJuD4f+7VmhO60k3aLSshYKpQ5vKT8PFpM96bPunA=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-15T17:21:45.8538664+00:00"}, "sUjQA/IRv5Z2uEPu/+yfLyob3nO3BXFS6TCzvdvL3Z4=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-15T17:21:45.8640017+00:00"}, "6My8bZpKoPvudtgJ/vLin0MjVHSRtdaRUO44IbtpRiQ=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-15T17:21:45.8640017+00:00"}, "ABvge/VxJNziWTfKsyJWabFapDn6gO3tCkiQFkTJOrw=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-15T17:21:45.8771351+00:00"}, "P8jH2zD81Y0T6w+hJgqRSuC474nP7RonTxmoJ/pUuXI=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-15T17:21:45.8803863+00:00"}, "CNzJLpF+Pqb18V0Irm6sY0lSjpK9L+y5AqjuTn7cZtY=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-15T17:21:45.813021+00:00"}, "L2cUlVEid2TrG3XKw47WaaTQuERiTljAYcL5h1Ht8qo=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-15T17:21:45.8462111+00:00"}, "THNQ6pasxvdkqKXNBCYLicsv/7g41xyQ0LeHvORIx3A=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\s1pscwwzre-lbqc3kgbxc.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "images/placeholder#[.{fingerprint=lbqc3kgbxc}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\images\\placeholder.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3c3m4rktjd", "Integrity": "YwonzJCms7EaWgMlPTqX7186fp/6+lt6I4LC2NDBfJI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\images\\placeholder.html", "FileLength": 589, "LastWriteTime": "2025-07-16T00:50:27.58842+00:00"}, "8JJQhUHi1HlyaiiFM0oASYKbwN03KBxBWV+UiYwL2X4=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\mnzfjppgbm-61n19gt1b8.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-15T17:21:45.8515511+00:00"}, "Q/MBYbhGIVstJlJY14hUW9b191XEOi4J60hWFSiSKyw=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\ydsimofm3k-j6uiw1e68n.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "css/site#[.{fingerprint=j6uiw1e68n}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ei2aiiynkt", "Integrity": "NuVjpxuTCIo8QD8yQnOZ9XAfLJQ0ViRevoHYmFi8oWI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\css\\site.css", "FileLength": 1157, "LastWriteTime": "2025-07-16T00:50:27.58842+00:00"}, "0fDnl6CMTQ3VrIoQWfvSDiTfCfR3r2GC39wMwrRNNcI=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\lme4py5qec-bqjiyaj88i.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-15T17:21:45.8538664+00:00"}, "41Sjivqydu5dpTYLUCGr4U80H6eiLHdcwe8c0xDQxrY=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\w0wo3w02eu-c2jlpeoesf.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-15T17:21:45.8538664+00:00"}, "Fnv0UEnYcITwoSvJqGs7W7yFD0kuCHja7fV1sB43srU=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\ojo50299ji-erw9l3u2r3.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-15T17:21:45.8538664+00:00"}, "I2QUmlFTwLPPj4Gw0CiC4EqWjOdNQfuYl0QU1E+F3F0=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\vzlfv8ayge-aexeepp0ev.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-15T17:21:45.8863825+00:00"}, "sA5NfNgfKl0nknXXWx1l8uJTbNFRkpZT1EtlMUU9C0U=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\cz72h42ci9-d7shbmvgxk.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-15T17:21:45.8886612+00:00"}, "ZQit54vtl6txxZfO7nY3WTCLUgeLOkZJ8wLvq7+Eh6I=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\bb2veg7dnb-ausgxo2sd3.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-15T17:21:45.8120211+00:00"}, "7rA1/iEJfcqpXxtWx+LubNgyzPiYruogeA3BMVPccn4=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\2r8trvsreu-k8d9w2qqmf.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-15T17:21:45.813021+00:00"}, "JJGlQ5bbK9DLuYr3wR9UQxWzujE5vl57Zx1deiqvp9I=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\8vsblbgcu8-cosvhxvwiu.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-15T17:21:45.8150219+00:00"}, "jvGhUFhkz5gsHlokUJf3QuEDhHZ46VIRLFgNXEgo1ec=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\moeu2gau1x-ub07r2b239.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-15T17:21:45.8150219+00:00"}, "ZVPaS/PKxUrm+e8vcnNE65apFQ0/tNLT9ERxMJGfUWI=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\kmfoz8ik7c-fvhpjtyr6v.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-15T17:21:45.8245147+00:00"}, "mZnE7vrsjqMJzjfndPUhgdA0XVAtfmRhAFKRYaF2bRs=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\v4v8jhlgh6-b7pk76d08c.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-15T17:21:45.8270281+00:00"}, "ZG1i5KKSOiwUcqmRGa4FP7Ke8sdfewXOX8Zp6+Hf8jE=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\syoe4xa1xn-fsbi9cje9m.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-15T17:21:45.8280276+00:00"}, "r5c6oy92TuIF5fOp6DQVUypu9Wni7+SdjFwVMTz5K/s=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\q6tfxd74d5-rzd6atqjts.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-15T17:21:45.8393333+00:00"}, "iYGpUGeuCQ9zEJnsstcDVsDVyeFVPRYOfQC23nbmJNE=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\d2c30w43dr-ee0r1s7dh0.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-15T17:21:45.8713731+00:00"}, "CkAbxyGB4LD0KNDtU5rg9nPh5WbSEU29uwg8MrfCR1o=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\yvgkg98a0u-dxx9fxp4il.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-15T17:21:45.8713731+00:00"}, "0/j8MSKoT9BYWJtJE5oirMbQXN7NGCBdVodjfccHQJU=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\11ya00ngah-jd9uben2k1.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-15T17:21:45.8100226+00:00"}, "7BvisFV0sAs5As/3KgpVD1CQo7+mb6coy62qnY1OMDQ=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\nkrdqkesd3-khv3u5hwcm.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-15T17:21:45.8110219+00:00"}, "pRzta8bFsuw26GhN/nAPRopY5G+qzwgtKJo9KmLiGf0=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\gpo65s6scb-r4e9w2rdcm.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-15T17:21:45.8160206+00:00"}, "uVxzfgr6mCfFjm/6E0wCnlht8ZiKBfVQop3Vi75QZUU=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\6jfli95l9p-lcd1t2u6c8.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-15T17:21:45.8180216+00:00"}, "DUzPw45+B+b84hOAN16xtdX49MPL1zu7rZLlMq6rJkM=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\4qp4ou20u9-c2oey78nd0.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-15T17:21:45.8200223+00:00"}, "CuOTUAFGhd43URiMU0KuZQgAD4OiSDs22f19QAORXZI=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\c2j2c6dw8y-tdbxkamptv.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-15T17:21:45.8230236+00:00"}, "aEo4tFpSc5QLco6NmrU2OJUqOVLEHb1416yRdrfBENQ=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\pfjvdxw8eh-j5mq2jizvt.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-15T17:21:45.8260291+00:00"}, "OuNanHqbzQ5ohV+ClhiDjFsUvyRL4mvHopTCK93zDvE=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\8omxv5drqg-06098lyss8.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-15T17:21:45.8280276+00:00"}, "hlMD9MMp2G+98KuNxJcANGrNFCpLMRxpD2aOOQ4gLuE=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\937c9an08q-nvvlpmu67g.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-15T17:21:45.8373313+00:00"}, "57+jU0dRO9jFpbcmrHF8UGLIR7SNjqZJgCBCTWI17Y0=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\2w1quu9rjj-s35ty4nyc5.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-15T17:21:45.8416894+00:00"}, "9sw2oXPa+JWOg9GRow4FPG+XoeQKwJMFg77ReoJ9eeI=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\edsb6dv727-pj5nd1wqec.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-15T17:21:45.821024+00:00"}, "btfkzqNlCtR4h2ASOZD+5OWjfNyhF+je3b+l19G4VPk=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\8mes9n6yvu-46ein0sx1k.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-15T17:21:45.8250216+00:00"}, "sbAOoYTD4fvf7SzvyRyYXMRBg7YC/7BZLyW/jg7VONk=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\trj50rn29p-v0zj4ognzu.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-15T17:21:45.8363316+00:00"}, "X8ckCuwf2WGkMdSaDa7blecSoT0c9ZuKOi7DAd7aosQ=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\gpxhnec3i7-37tfw0ft22.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-15T17:21:45.8426969+00:00"}, "sLixJgmzD1P1oBwkxzZmh3huZaXJUqb6zE1nkUV/Rms=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\mm0zi9y2mv-hrwsygsryq.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-15T17:21:45.8538664+00:00"}, "hduGZzBSrLWLvPRlJS94PvYKmb08tq8bj77VVxzzYOQ=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\1gj28wiugc-pk9g2wxc8p.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-15T17:21:45.8538664+00:00"}, "cwP6wQXVtKtnY4ZR97/z9SlIxvn4wQeLnK61btVmOdE=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\bhizbd91dp-ft3s53vfgj.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-15T17:21:45.8843837+00:00"}, "HFZnV+XiLFpUNoMMAjJX/FTzlbAsYHUEKnD3d4O7siw=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\n2gymnpw05-6cfz1n2cew.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-15T17:21:45.8462111+00:00"}, "JzKMrRznersEjJV/umWv3lUIAxdWAql4LyhAnllOwXs=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\fye280x2tg-6pdc2jztkx.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-15T17:21:45.8619917+00:00"}, "+fXZ9y2CdvVWGx3LHyDCIqK6JkVZX2lUVWa4Esz4vNU=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\vud0cmacte-493y06b0oq.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-15T17:21:45.8781492+00:00"}, "CYKuLubPKWT7Hvy5fSYH54o2ZaA5jBrHrnVWDl+c2Xk=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\hszg8sna6n-iovd86k7lj.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-15T17:21:45.8160206+00:00"}, "uQPWZRqGRXZWGdQhEhUStABvKGEC7UtpHxIjZpkED3U=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\jquxvjdviv-vr1egmr9el.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-15T17:21:45.8190217+00:00"}, "EkwF6e3JM2etW9njQ3SpTYodl4CBbICjx3z9cqWd8Oo=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\gy4ponhhdp-kbrnm935zg.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-15T17:21:45.8270281+00:00"}, "JRsGsWXWFF36anjM/SzF5NKLQ1aMg8Z9g/LH4j2uMkA=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\87iu4sxos0-jj8uyg4cgr.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-15T17:21:45.840486+00:00"}, "a4YhvPNU5RZmok2U0CR0H1dyfDxjaU5onBzpNUFi3xU=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\z5ae4c3rnc-y7v9cxd14o.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-15T17:21:45.8452039+00:00"}, "ofqjlFb4jBoHTMN/c25t7rpuhi6aihvKmxHMgPZe7Ks=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\v4zx9fgw6t-notf2xhcfb.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-15T17:21:45.8538664+00:00"}, "c8ttY8UYcI5Va1e1TDPnEcF8exRDNP/3FdJDJVkzmzE=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\nj8wg5poap-h1s4sie4z3.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-15T17:21:45.8619917+00:00"}, "iWbdY6PPgtxOppxQK22Dy+hcDX0oI7mA2Y0G1EdQgCg=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\p45gpm7vec-63fj8s7r0e.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-15T17:21:45.8833825+00:00"}, "Kvc+Rqw6ItjHpPr4pauFRD6Ozrmhze/k+/6NQ6e0x/Q=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\6ja2o8s4au-0j3bgjxly4.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-15T17:21:45.8886612+00:00"}, "d6NgOCb5Ec3ksYVmzKaXHjOm0zklMGGLydq8Q5d6LNI=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\jfeth2yuyt-47otxtyo56.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-15T17:21:45.8886612+00:00"}, "Ys96MAzd6yBcJT+AnB7IaiCY6yjlcjN8iZFpjPMWlgg=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\lsoyonkgh6-4v8eqarkd7.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-15T17:21:45.8090139+00:00"}, "Pt8+EZz/7i50+RxLcHk+tBkUlJOPIYtnOT0ru7GMK7A=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\wj4o34evrm-356vix0kms.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-15T17:21:45.8100226+00:00"}, "czxyNFgUlG3clEZVOeSBwDcts+s0sBq2JBFqxepRYJQ=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\1mm30zfehd-83jwlth58m.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-15T17:21:45.8120211+00:00"}, "V3hC2sXYPs/htHXMap1QNedRtX4KveE2dYSuU1FyC7M=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\r5adkz6v91-mrlpezrjn3.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-15T17:21:45.8230236+00:00"}, "KVDHHlTv9w1vhdW03l9kB6QnYMYugZXt60J1w30ELZM=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\6hdckvaymm-lzl9nlhx6b.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-15T17:21:45.8353229+00:00"}, "jJ8pATp9GcCrpPLpPGm1hGbDV480NdQxJO/cq9SQjlY=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\pcldeyzz5h-ag7o75518u.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-15T17:21:45.8373313+00:00"}, "nfsi6GywDn3g7ziUNLtdfu41F7q4+UvinDjma5Demgw=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\1xmm58uqzt-x0q3zqp4vz.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-15T17:21:45.8393333+00:00"}, "DWDzfv/4F4SysBm/dOD63mQwxs4gO3GJRAbEiZHDS88=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\eigr9dwutc-0i3buxo5is.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-15T17:21:45.8462111+00:00"}, "lswOGmjE8VED2PrO2kc7e3XleYbiNjBmu+Mk9V2lP50=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\mnwymsaoi3-o1o13a6vjx.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-15T17:21:45.8538664+00:00"}, "f7NujZZ2zkEV1DLmL24sq1iQnfq62drB4AYYUXUfRbo=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\8cji0s8b40-ttgo8qnofa.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-15T17:21:45.860981+00:00"}, "UaL7XmGJo6NsWLAYNzAlDrKi2ZfuJApRjabESu+t26E=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\j636vtnu0d-2z0ns9nrw6.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-15T17:21:45.8140209+00:00"}, "tDbywowiEUyk9D51MyAor6Ka6EOtWVD2CG3tQ4GXBjs=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\fx0y26tojq-muycvpuwrr.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-15T17:21:45.8160206+00:00"}, "LFrP0rsjytAUvMyoyJudx25xahTRX/v1yAKzrGu9fcw=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\68jf9bnm1b-87fc7y1x7t.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-15T17:21:45.8190217+00:00"}, "Kzl5aAJG/AJzkcwflST9NK/a0aP5A6iOur6h+0jjE20=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\swxwyoee87-mlv21k5csn.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-15T17:21:45.8260291+00:00"}, "nCmPS9dCPfLwUMagbpfjxp8frBz+rynhiwxtglb0bOs=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\e11ejxkqff-fg2jea2nkz.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "ShoeShopAnNhien#[.{fingerprint=fg2jea2nkz}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\scopedcss\\bundle\\ShoeShopAnNhien.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ap3b4ppgq9", "Integrity": "fv+CZkVK+zdS/iS9r2sVjkqivQDVVUAvr6EOXIdCcxI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\scopedcss\\bundle\\ShoeShopAnNhien.styles.css", "FileLength": 545, "LastWriteTime": "2025-07-15T17:21:45.832028+00:00"}, "SQwyy18yJ4R90pVTVAos1ng5I134t4zCH5FI5voxdhA=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\12kmsfh0gf-fg2jea2nkz.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "ShoeShopAnNhien#[.{fingerprint=fg2jea2nkz}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\ShoeShopAnNhien.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ap3b4ppgq9", "Integrity": "fv+CZkVK+zdS/iS9r2sVjkqivQDVVUAvr6EOXIdCcxI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\ShoeShopAnNhien.bundle.scp.css", "FileLength": 545, "LastWriteTime": "2025-07-15T17:21:45.8483034+00:00"}, "ya1RDkcSYejDjXd3xBnX6Bp+ACY+6rb1Z9QUFvyEnpI=": {"Identity": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\r7zbt8o7nw-i59wnfu0tj.gz", "SourceId": "ShoeShopAnNhien", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeShopAnNhien", "RelativePath": "js/site#[.{fingerprint=i59wnfu0tj}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "axptoazj6q", "Integrity": "fpgCJMfBfTgqxqCauOBm+hxLCMrdX8UZT1KfIkPP94E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\ShoeShopAnNhien\\wwwroot\\js\\site.js", "FileLength": 1593, "LastWriteTime": "2025-07-16T00:50:27.58842+00:00"}}, "CachedCopyCandidates": {}}