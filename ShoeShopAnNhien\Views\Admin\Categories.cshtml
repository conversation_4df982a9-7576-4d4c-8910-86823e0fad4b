@model List<ShoeShopAnNhien.Models.Category>
@{
    ViewData["Title"] = "Quản Lý <PERSON> - Admin Panel";
    Layout = "_AdminLayout";
}

<!-- Page Header -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="h3 mb-0">Quản <PERSON></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Admin")">Dashboard</a></li>
                    <li class="breadcrumb-item active"><PERSON><PERSON></li>
                </ol>
            </nav>
        </div>
        <div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                <i class="fas fa-plus me-2"></i>Th<PERSON><PERSON>
            </button>
        </div>
    </div>
</div>

<!-- Categories Grid -->
<div class="row">
    @if (Model.Any())
    {
        @foreach (var category in Model)
        {
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">@category.Name</h6>
                        <span class="badge @(category.IsActive ? "bg-success" : "bg-secondary")">
                            @(category.IsActive ? "Hoạt động" : "Tạm dừng")
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <img src="@(category.ImageUrl ?? "/images/categories/default.jpg")" 
                                 alt="@category.Name" 
                                 class="img-fluid rounded" 
                                 style="width: 100px; height: 100px; object-fit: cover;">
                        </div>
                        <p class="card-text">@(category.Description ?? "Chưa có mô tả")</p>
                        <div class="row text-center">
                            <div class="col-6">
                                <h6 class="text-primary">@category.Products.Count</h6>
                                <small class="text-muted">Sản phẩm</small>
                            </div>
                            <div class="col-6">
                                <h6 class="text-success">@category.CreatedAt.ToString("dd/MM/yyyy")</h6>
                                <small class="text-muted">Ngày tạo</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <button class="btn btn-outline-primary btn-sm" data-bs-toggle="tooltip" title="Chỉnh sửa">
                                <i class="fas fa-edit"></i> Sửa
                            </button>
                            <button class="btn btn-outline-@(category.IsActive ? "secondary" : "success") btn-sm" 
                                    data-bs-toggle="tooltip" 
                                    title="@(category.IsActive ? "Tạm dừng" : "Kích hoạt")">
                                <i class="fas fa-@(category.IsActive ? "pause" : "play")"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm" data-bs-toggle="tooltip" title="Xóa">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-tags text-muted" style="font-size: 4rem; opacity: 0.3;"></i>
                <h5 class="mt-3 text-muted">Chưa có danh mục nào</h5>
                <p class="text-muted">Hãy thêm danh mục đầu tiên cho cửa hàng</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                    <i class="fas fa-plus me-2"></i>Thêm Danh Mục
                </button>
            </div>
        </div>
    }
</div>

<!-- Categories Table View -->
<div class="card admin-table mt-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Danh Sách Chi Tiết</h5>
        <div>
            <button class="btn btn-outline-secondary btn-sm" onclick="exportToCSV('categoriesTable', 'categories.csv')">
                <i class="fas fa-download me-1"></i>Xuất CSV
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="categoriesTable">
                    <thead>
                        <tr>
                            <th>Hình Ảnh</th>
                            <th>Tên Danh Mục</th>
                            <th>Mô Tả</th>
                            <th>Số Sản Phẩm</th>
                            <th>Trạng Thái</th>
                            <th>Ngày Tạo</th>
                            <th>Thao Tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var category in Model)
                        {
                            <tr>
                                <td>
                                    <img src="@(category.ImageUrl ?? "/images/categories/default.jpg")" 
                                         alt="@category.Name" 
                                         class="rounded" 
                                         style="width: 50px; height: 50px; object-fit: cover;">
                                </td>
                                <td>
                                    <strong>@category.Name</strong>
                                </td>
                                <td>
                                    @(category.Description ?? "Chưa có mô tả")
                                </td>
                                <td>
                                    <span class="badge bg-primary">@category.Products.Count sản phẩm</span>
                                </td>
                                <td>
                                    <span class="badge @(category.IsActive ? "bg-success" : "bg-secondary")">
                                        @(category.IsActive ? "Hoạt động" : "Tạm dừng")
                                    </span>
                                </td>
                                <td>
                                    @category.CreatedAt.ToString("dd/MM/yyyy")
                                    <br><small class="text-muted">@category.CreatedAt.ToString("HH:mm")</small>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-outline-primary btn-sm" data-bs-toggle="tooltip" title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" data-bs-toggle="tooltip" title="Xem sản phẩm">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" data-bs-toggle="tooltip" title="Xóa">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm Danh Mục Mới</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCategoryForm">
                    <div class="mb-3">
                        <label class="form-label">Tên Danh Mục *</label>
                        <input type="text" class="form-control" name="Name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mô Tả</label>
                        <textarea class="form-control" name="Description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Hình Ảnh</label>
                        <input type="file" class="form-control" name="ImageFile" accept="image/*">
                        <small class="form-text text-muted">Chọn hình ảnh đại diện cho danh mục</small>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="IsActive" id="isActive" checked>
                            <label class="form-check-label" for="isActive">
                                Kích hoạt danh mục
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="saveCategory()">
                    <i class="fas fa-save me-2"></i>Lưu Danh Mục
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function saveCategory() {
    // This would normally submit to server
    showToast('Chức năng thêm danh mục sẽ được phát triển trong phiên bản tiếp theo!', 'info');
}
</script>
