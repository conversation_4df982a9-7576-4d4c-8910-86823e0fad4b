@model List<ShoeShopAnNhien.Models.Product>
@{
    ViewData["Title"] = "Quản Lý Sản Phẩm - Admin Panel";
    Layout = "_AdminLayout";
}

<!-- Page Header -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="h3 mb-0">Quản Lý Sản Phẩm</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Admin")">Dashboard</a></li>
                    <li class="breadcrumb-item active">Sản Phẩm</li>
                </ol>
            </nav>
        </div>
        <div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                <i class="fas fa-plus me-2"></i>Thê<PERSON>
            </button>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <input type="text" class="form-control" id="searchProducts" placeholder="Tìm kiếm sản phẩm..." data-table-search="productsTable">
            </div>
            <div class="col-md-2">
                <select class="form-select" id="filterCategory">
                    <option value="">Tất cả danh mục</option>
                    <!-- Categories will be populated here -->
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="filterBrand">
                    <option value="">Tất cả thương hiệu</option>
                    <!-- Brands will be populated here -->
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="filterStatus">
                    <option value="">Tất cả trạng thái</option>
                    <option value="active">Hoạt động</option>
                    <option value="inactive">Tạm dừng</option>
                </select>
            </div>
            <div class="col-md-3">
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-secondary" onclick="exportToCSV('productsTable', 'products.csv')">
                        <i class="fas fa-download me-1"></i>Xuất CSV
                    </button>
                    <button class="btn btn-outline-secondary" onclick="printTable('productsTable')">
                        <i class="fas fa-print me-1"></i>In
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Products Table -->
<div class="card admin-table">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Danh Sách Sản Phẩm (@ViewBag.TotalItems sản phẩm)</h5>
        <div class="d-flex align-items-center">
            <span class="text-muted me-3">Trang @ViewBag.CurrentPage / @ViewBag.TotalPages</span>
        </div>
    </div>
    <div class="card-body p-0">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="productsTable">
                    <thead>
                        <tr>
                            <th>Hình Ảnh</th>
                            <th>Tên Sản Phẩm</th>
                            <th>SKU</th>
                            <th>Danh Mục</th>
                            <th>Thương Hiệu</th>
                            <th>Giá</th>
                            <th>Tồn Kho</th>
                            <th>Trạng Thái</th>
                            <th>Thao Tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var product in Model)
                        {
                            <tr>
                                <td>
                                    <img src="@(product.MainImageUrl ?? "/images/products/default.jpg")" 
                                         alt="@product.Name" 
                                         class="product-image"
                                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 5px;">
                                </td>
                                <td>
                                    <div>
                                        <h6 class="mb-1">@product.Name</h6>
                                        @if (product.IsFeatured)
                                        {
                                            <span class="badge bg-warning text-dark">Nổi bật</span>
                                        }
                                    </div>
                                </td>
                                <td>
                                    <code>@product.SKU</code>
                                </td>
                                <td>@product.Category.Name</td>
                                <td>@product.Brand.Name</td>
                                <td>
                                    @if (product.IsOnSale)
                                    {
                                        <span class="text-danger fw-bold">@product.SalePrice?.ToString("N0") VNĐ</span>
                                        <br><small class="text-muted text-decoration-line-through">@product.Price.ToString("N0") VNĐ</small>
                                    }
                                    else
                                    {
                                        <span class="fw-bold">@product.Price.ToString("N0") VNĐ</span>
                                    }
                                </td>
                                <td>
                                    <span class="badge @(product.StockQuantity <= 10 ? "bg-danger" : product.StockQuantity <= 20 ? "bg-warning" : "bg-success")">
                                        @product.StockQuantity
                                    </span>
                                </td>
                                <td>
                                    <span class="badge @(product.IsActive ? "bg-success" : "bg-secondary")">
                                        @(product.IsActive ? "Hoạt động" : "Tạm dừng")
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-outline-primary btn-sm" data-bs-toggle="tooltip" title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm" data-bs-toggle="tooltip" title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-@(product.IsActive ? "secondary" : "success") btn-sm" 
                                                onclick="toggleProductStatus(@product.Id)" 
                                                data-bs-toggle="tooltip" 
                                                title="@(product.IsActive ? "Tạm dừng" : "Kích hoạt")">
                                            <i class="fas fa-@(product.IsActive ? "pause" : "play")"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" data-bs-toggle="tooltip" title="Xóa">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="fas fa-box text-muted" style="font-size: 4rem; opacity: 0.3;"></i>
                <h5 class="mt-3 text-muted">Chưa có sản phẩm nào</h5>
                <p class="text-muted">Hãy thêm sản phẩm đầu tiên cho cửa hàng</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                    <i class="fas fa-plus me-2"></i>Thêm Sản Phẩm
                </button>
            </div>
        }
    </div>
</div>

<!-- Pagination -->
@if (ViewBag.TotalPages > 1)
{
    <nav aria-label="Products pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            @if (ViewBag.CurrentPage > 1)
            {
                <li class="page-item">
                    <a class="page-link" href="@Url.Action("Products", new { page = ViewBag.CurrentPage - 1 })">
                        <i class="fas fa-chevron-left"></i> Trước
                    </a>
                </li>
            }

            @for (int i = Math.Max(1, ViewBag.CurrentPage - 2); i <= Math.Min(ViewBag.TotalPages, ViewBag.CurrentPage + 2); i++)
            {
                <li class="page-item @(i == ViewBag.CurrentPage ? "active" : "")">
                    <a class="page-link" href="@Url.Action("Products", new { page = i })">@i</a>
                </li>
            }

            @if (ViewBag.CurrentPage < ViewBag.TotalPages)
            {
                <li class="page-item">
                    <a class="page-link" href="@Url.Action("Products", new { page = ViewBag.CurrentPage + 1 })">
                        Sau <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            }
        </ul>
    </nav>
}

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm Sản Phẩm Mới</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addProductForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Tên Sản Phẩm *</label>
                            <input type="text" class="form-control" name="Name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">SKU</label>
                            <input type="text" class="form-control" name="SKU">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mô Tả *</label>
                        <textarea class="form-control" name="Description" rows="3" required></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Giá *</label>
                            <input type="number" class="form-control" name="Price" min="0" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Giá Khuyến Mãi</label>
                            <input type="number" class="form-control" name="SalePrice" min="0">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Số Lượng *</label>
                            <input type="number" class="form-control" name="StockQuantity" min="0" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Danh Mục *</label>
                            <select class="form-select" name="CategoryId" required>
                                <option value="">Chọn danh mục</option>
                                <!-- Categories will be populated here -->
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Thương Hiệu *</label>
                            <select class="form-select" name="BrandId" required>
                                <option value="">Chọn thương hiệu</option>
                                <!-- Brands will be populated here -->
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Sizes Có Sẵn</label>
                            <input type="text" class="form-control" name="AvailableSizes" placeholder="36,37,38,39,40,41,42">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Màu Sắc Có Sẵn</label>
                            <input type="text" class="form-control" name="AvailableColors" placeholder="Đen,Trắng,Xanh">
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="IsFeatured" id="isFeatured">
                            <label class="form-check-label" for="isFeatured">
                                Sản phẩm nổi bật
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="saveProduct()">
                    <i class="fas fa-save me-2"></i>Lưu Sản Phẩm
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function saveProduct() {
    // This would normally submit to server
    showToast('Chức năng thêm sản phẩm sẽ được phát triển trong phiên bản tiếp theo!', 'info');
}
</script>
